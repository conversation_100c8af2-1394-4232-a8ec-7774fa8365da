# Import necessary packages
import pandas as pd
import numpy as np
from thefuzz import process

prices = pd.read_csv('data/airbnb_price.csv')
reviews = pd.read_csv('data/airbnb_last_review.tsv', sep='\t')
room_types = pd.read_excel('data/airbnb_room_type.xlsx')

df = pd.merge(prices, reviews, on='listing_id', how='outer')
df = pd.merge(df, room_types, on='listing_id', how='outer')

df.to_csv('data/airbnb.csv', index=False)
print(df.head())
print(df.info())

df['last_review'] = pd.to_datetime(df['last_review'])
df['room_type'] = df['room_type'].astype('category')
df['price'] = df['price'].str.strip("dollars").astype("int")

df.info()

first_reviewed = df['last_review'].min()
last_reviewed = df['last_review'].max()
print(f"First review: {first_reviewed}, Last review: {last_reviewed}")

print(df['room_type'].unique())

categories = pd.DataFrame({'room_type': ['shared rooms', 'private room', 'entire homes/apartments']})
categories
    



nb_private_rooms = df['room_type'].value_counts()['Private room']
print(f"Number of private rooms: {nb_private_rooms}")

avg_price = round(df['price'].mean(), 2)
print(f"Average price: {avg_price}")

review_dates = pd.DataFrame({"first_review": first_reviewed, "last_review": last_reviewed, "nb_private_rooms": nb_private_rooms, "avg_price": avg_price}, index=[0])
print(review_dates)